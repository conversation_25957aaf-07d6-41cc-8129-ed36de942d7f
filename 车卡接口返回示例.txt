{"actions": [{"action": "", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-plus", "id": "01545c1541be8a8ae61a538908b423b4", "key": "", "legend": "", "list": "", "method": "POST", "name": "新增", "primary": true, "rule": "", "selected": "none", "view": ""}, {"group": "1", "i18nCode": "", "icon": "glyphicon-pencil", "id": "015505ec4ca18a8ae61a55011d67024c", "key": "", "method": "PUT", "name": "修改", "primary": false, "selected": "single"}, {"group": "", "i18nCode": "", "icon": "glyphicon-trash", "id": "015505ec4ca48a8ae61a55011d67024d", "key": "", "method": "DELETE", "name": "删除", "primary": false, "selected": "somewhat"}, {"action": "batch", "actionParam": "{\"update\":{\"then\":{\"card_status\":\"Y\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-random", "id": "015562c3c94a8a8ae61a555d0e3b0721", "key": "use", "legend": "", "list": "", "method": "POST", "name": "发放", "primary": true, "rule": "", "selected": "somewhat", "view": ""}, {"action": "one", "actionParam": "{\"update\":{\"then\":{\"card_status\":\"N\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-retweet", "id": "015562c4be458a8ae61a555d0e3b0724", "key": "unuse", "legend": "车卡回收，判断在用转移单为联系外运及出库状态不可操作", "list": "", "method": "POST", "name": "回收", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "batch", "actionParam": "{\"update\":{\"then\":{\"card_status\":\"N\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-retweet", "id": "01576e79d7b28a8ae61a576bc7590022", "key": "recycle", "legend": "车卡回收,不对车卡当前使用情况判断", "list": "", "method": "POST", "name": "强制回收", "primary": false, "rule": "", "selected": "somewhat", "view": ""}], "data": {"collection": [{"card_id": "2B18722C", "card_code": "车卡10_宁波容威", "card_name": "宁波容威车卡10", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1474959731947}, {"card_id": "2B23303D", "card_code": "车卡09_宁波容威", "card_name": "万华容威车卡09", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "N", "update_time": 1474959582087}, {"card_id": "DB1AE0E4", "card_code": "车卡08", "card_name": "万华宁波固废管理系统车卡08", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737161368}, {"card_id": "3B3372E0", "card_code": "车卡07", "card_name": "万华宁波固废管理系统车卡07", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737135435}, {"card_id": "E5FD4AB4", "card_code": "车卡06", "card_name": "万华宁波固废管理系统车卡06", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737108900}, {"card_id": "2B12FBDA", "card_code": "车卡05", "card_name": "万华宁波固废管理系统车卡05", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737079096}, {"card_id": "2B134427", "card_code": "车卡04", "card_name": "万华宁波固废管理系统车卡04", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737050340}, {"card_id": "A5C79A11", "card_code": "车卡03", "card_name": "万华宁波固废管理系统车卡03", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473737021493}, {"card_id": "2B1313AC", "card_code": "车卡02", "card_name": "万华宁波固废管理系统车卡02", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473736994422}, {"card_id": "2B135D03", "card_code": "车卡01", "card_name": "万华宁波固废管理系统车卡01", "card_object": "", "card_freq": "HF 高频 13.56MHz", "card_standard": "", "card_factory": "", "card_remark": "", "card_status": "Y", "update_time": 1473736953584}], "count": 10, "exists": false, "pagination": {"count": 1, "current": 1, "paging": true, "size": 15}}, "extend": "", "handle": "mulpitle", "icon": "", "id": "015505ec4c498a8ae61a55011d670238", "layout": "", "legend": "IC卡信息视图", "mode": "table", "name": "车卡信息", "schema": {"async": false, "attributes": [{"access": "<PERSON><PERSON>ly", "alias": "物理串号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "车卡NFC物理串号", "list": "", "maxLength": 64, "metaType": "String", "name": "card_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "<PERSON><PERSON>ly", "alias": "卡号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "车卡编号", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车牌号", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_object", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": []}, "extend": "", "handle": "", "icon": "", "id": "0154370cf0088a8ae61a538908b40bfe", "layout": "", "legend": "视图", "mode": "table", "name": "车辆字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "车辆主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车牌号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "car_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车辆类型", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "槽罐车,中型卡车,大型卡车,特级车辆,重型厢式货车,重型普通货车,重型仓栅式货车,重型半挂牵引车,管道输送", "maxLength": 64, "metaType": "String", "name": "car_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输证号", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "有效期", "control": "date", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "valid_date", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "hidden": "list", "i18nCode": "", "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "有效,无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "区域主键", "column": 6, "control": "", "dataType": "id", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "region_id", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "0155723237938a8ae61a5570bbef10e4", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "car_id", "nodeIdName": "car_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "车牌号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "object_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "频率", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "HF 高频 13.56MHz", "maxLength": 64, "metaType": "String", "name": "card_freq", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "HF 高频 13.56MHz", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "规格", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_standard", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "生产厂家", "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_factory", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "有效期", "control": "date", "dataType": "date", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "card_validity", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "hidden": "list", "i18nCode": "", "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "card_remark", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "是否在用", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "Y:是,N:否", "maxLength": 64, "metaType": "String", "name": "card_status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "N", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "更新时间", "control": "datetime", "dataType": "time", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "update_time", "remind": "", "required": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "1755837450137", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "卡号", "control": "", "controlParams": "", "isAid": false, "isRange": false, "name": "card_code", "required": false, "value": ""}, {"alias": "名称", "control": "text", "isRange": false, "name": "card_name", "required": false, "value": ""}, {"alias": "车牌号", "control": "chosen", "isRange": false, "name": "card_object", "required": false, "value": ""}], "idName": "card_id", "nodeIdName": "card_id"}, "setting": "", "template": "", "type": "standard"}